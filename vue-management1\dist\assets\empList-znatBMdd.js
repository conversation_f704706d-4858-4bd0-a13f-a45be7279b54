import{g as y}from"./api-BgzO6Ksg.js";import{r as V,h as S,i as J,c,f as i,j as K,b as t,g as Q,s as R,a as m,w as a,k as W,l as X,F as Y,o as C,e as f,t as _,E as g}from"./index-6989FsBU.js";const Z={style:{"margin-bottom":"10px"}},h={style:{display:"flex","align-items":"center"}},ee={style:{"margin-left":"10px"}},le={key:0},te={key:1},ae={class:"dialog-footer"},oe={class:"dialog-footer"},ue={__name:"empList",setup(de){const D=V(!0),v=V(!1),b=V(!1),U=V(1),$=V(0),d=S({}),n=S({}),E=V(""),z=()=>{b.value=!0},P=async()=>{console.log(n);const o=await y("/addEmp",n);o.code==200?(b.value=!1,w(),g({title:"Success",message:o.message,type:"success"})):g({title:"Error",message:o.message,type:"error"})},j=o=>{U.value=o,w()},F=async()=>{const o=await y("/selectLikeUsername",{username:E.value});x.value=o.data},L=(o,e)=>{v.value=!0,Object.assign(d,e)},T=()=>{M()},M=async()=>{const o=await y("/updateEmployee",d);o.code==200?(w(),v.value=!1,g({title:"Success",message:o.message,type:"success"})):g({title:"Error",message:o.message,type:"error"})},O=async(o,e)=>{const s=await y("/deleteEmp",{id:e.id});s.code==200?(x.value.splice(o,1),w(),v.value=!1,g({title:"Success",message:s.message,type:"success"})):g({title:"Error",message:s.message,type:"error"})},x=V([]),w=async()=>{const o=await y("/getUserInfo",{currentPage:U.value});x.value=o.data,$.value=o.code,setTimeout(()=>{D.value=!1},300)};return J(async()=>{w()}),(o,e)=>{const s=m("el-input"),p=m("el-button"),r=m("el-table-column"),q=m("el-table"),A=m("el-pagination"),u=m("el-form-item"),k=m("el-option"),N=m("el-select"),B=m("el-form"),I=m("el-dialog"),G=W("loading");return C(),c(Y,null,[i("div",Z,[t(s,{modelValue:E.value,"onUpdate:modelValue":e[0]||(e[0]=l=>E.value=l),style:{width:"240px"},placeholder:"请输入用户名","prefix-icon":Q(R)},null,8,["modelValue","prefix-icon"]),t(p,{type:"danger",onClick:F},{default:a(()=>e[20]||(e[20]=[f("搜索",-1)])),_:1,__:[20]})]),i("div",null,[t(p,{type:"warning",onClick:z},{default:a(()=>e[21]||(e[21]=[f("添加员工",-1)])),_:1,__:[21]})]),K((C(),X(q,{data:x.value,style:{width:"90%"}},{default:a(()=>[t(r,{label:"员工编号",width:"120"},{default:a(l=>[i("div",h,[i("span",ee,_(l.row.id),1)])]),_:1}),t(r,{label:"员工姓名",width:"120"},{default:a(l=>[i("div",null,_(l.row.name),1)]),_:1}),t(r,{label:"年龄",width:"120"},{default:a(l=>[i("div",null,_(l.row.age),1)]),_:1}),t(r,{label:"电话",width:"130"},{default:a(l=>[i("div",null,_(l.row.phone),1)]),_:1}),t(r,{label:"邮箱",width:"120"},{default:a(l=>[i("div",null,_(l.row.email),1)]),_:1}),t(r,{label:"部门编号",width:"100"},{default:a(l=>[i("div",null,_(l.row.departmentId),1)]),_:1}),t(r,{label:"职位",width:"120"},{default:a(l=>[i("div",null,_(l.row.position),1)]),_:1}),t(r,{label:"状态",width:"120"},{default:a(l=>[l.row.position==1?(C(),c("div",le,"在职")):(C(),c("div",te,"离职"))]),_:1}),t(r,{label:"操作"},{default:a(l=>[t(p,{size:"small",onClick:H=>L(l.$index,l.row)},{default:a(()=>e[22]||(e[22]=[f(" 编辑 ",-1)])),_:2,__:[22]},1032,["onClick"]),t(p,{size:"small",type:"danger",onClick:H=>O(l.$index,l.row)},{default:a(()=>e[23]||(e[23]=[f(" 删除 ",-1)])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[G,D.value]]),t(A,{"current-page":U.value,"onUpdate:currentPage":e[1]||(e[1]=l=>U.value=l),"page-size":2,layout:"prev, pager, next",total:$.value,onCurrentChange:j},null,8,["current-page","total"]),t(I,{modelValue:v.value,"onUpdate:modelValue":e[10]||(e[10]=l=>v.value=l),title:"员工添加",width:"500"},{footer:a(()=>[i("div",ae,[t(p,{onClick:e[9]||(e[9]=l=>v.value=!1)},{default:a(()=>e[24]||(e[24]=[f("取消",-1)])),_:1,__:[24]}),t(p,{type:"primary",onClick:T},{default:a(()=>e[25]||(e[25]=[f(" 确认 ",-1)])),_:1,__:[25]})])]),default:a(()=>[t(B,{model:d,"label-width":"auto",style:{"max-width":"600px"}},{default:a(()=>[t(u,{label:"用户编号"},{default:a(()=>[t(s,{modelValue:d.id,"onUpdate:modelValue":e[2]||(e[2]=l=>d.id=l),disabled:""},null,8,["modelValue"])]),_:1}),t(u,{label:"员工姓名"},{default:a(()=>[t(s,{modelValue:d.name,"onUpdate:modelValue":e[3]||(e[3]=l=>d.name=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"年龄"},{default:a(()=>[t(s,{modelValue:d.age,"onUpdate:modelValue":e[4]||(e[4]=l=>d.age=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"电话"},{default:a(()=>[t(s,{modelValue:d.phone,"onUpdate:modelValue":e[5]||(e[5]=l=>d.phone=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"邮箱"},{default:a(()=>[t(s,{modelValue:d.email,"onUpdate:modelValue":e[6]||(e[6]=l=>d.email=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"职位"},{default:a(()=>[t(s,{modelValue:d.position,"onUpdate:modelValue":e[7]||(e[7]=l=>d.position=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:a(()=>[t(N,{modelValue:d.status,"onUpdate:modelValue":e[8]||(e[8]=l=>d.status=l),placeholder:"请选择状态"},{default:a(()=>[t(k,{label:"在职",value:1}),t(k,{label:"离职",value:0})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(I,{modelValue:b.value,"onUpdate:modelValue":e[19]||(e[19]=l=>b.value=l),title:"用户修改",width:"500"},{footer:a(()=>[i("div",oe,[t(p,{onClick:e[18]||(e[18]=l=>b.value=!1)},{default:a(()=>e[26]||(e[26]=[f("取消",-1)])),_:1,__:[26]}),t(p,{type:"primary",onClick:P},{default:a(()=>e[27]||(e[27]=[f(" 确认 ",-1)])),_:1,__:[27]})])]),default:a(()=>[t(B,{model:n,"label-width":"auto",style:{"max-width":"600px"}},{default:a(()=>[t(u,{label:"用户名"},{default:a(()=>[t(s,{modelValue:n.name,"onUpdate:modelValue":e[11]||(e[11]=l=>n.name=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"年龄"},{default:a(()=>[t(s,{modelValue:n.age,"onUpdate:modelValue":e[12]||(e[12]=l=>n.age=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"电话"},{default:a(()=>[t(s,{modelValue:n.phone,"onUpdate:modelValue":e[13]||(e[13]=l=>n.phone=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"邮箱"},{default:a(()=>[t(s,{modelValue:n.email,"onUpdate:modelValue":e[14]||(e[14]=l=>n.email=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"部门"},{default:a(()=>[t(s,{modelValue:n.departmentId,"onUpdate:modelValue":e[15]||(e[15]=l=>n.departmentId=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"职位"},{default:a(()=>[t(s,{modelValue:n.position,"onUpdate:modelValue":e[16]||(e[16]=l=>n.position=l)},null,8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:a(()=>[t(N,{modelValue:n.status,"onUpdate:modelValue":e[17]||(e[17]=l=>n.status=l),placeholder:"请选择状态"},{default:a(()=>[t(k,{label:"在职",value:1}),t(k,{label:"离职",value:0})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}};export{ue as default};
