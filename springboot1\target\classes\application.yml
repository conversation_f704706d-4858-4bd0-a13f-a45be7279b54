server:
  port: 8082

spring:
  application:
    name: springboot
  datasource:
    username: root
    password: root1234
    url: ***********************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5
  redis:
    database: 0
    host: localhost
    port: 6379
    #password: 123456
    timeout: 5000 # 连接超时时间（毫秒）
    lettuce:
      pool:
        max-active: 20 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000ms # 连接池最大阻塞等待时间（使用负值表示没有限制）




mybatis:
  # 指定 mapper.xml 的位置
  mapper-locations: classpath:mapping/*.xml
  #扫描实体类的位置,在此处指明扫描实体类的包，在 mapper.xml 中就可以不写实体类的全路径名
  type-aliases-package: entity
  configuration:
    #默认开启驼峰命名法，可以不用设置该属性
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


