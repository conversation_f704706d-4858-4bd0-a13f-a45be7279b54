package qidian.it.springboot.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.beans.factory.annotation.Autowired;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.EmployeeServiceImpl;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@SpringJUnitConfig
public class EmployeeServiceTest {

    @Autowired
    private EmployeeServiceImpl employeeService;

    @Test
    public void testAddEmployeeWithUniquePhone() {
        // 创建第一个员工
        Employee employee1 = new Employee();
        employee1.setName("张三");
        employee1.setPhone("13800138001");
        employee1.setEmail("<EMAIL>");
        
        Result result1 = employeeService.addEmployee(employee1);
        assertEquals(200, result1.getCode());
        
        // 尝试创建具有相同联系方式的第二个员工
        Employee employee2 = new Employee();
        employee2.setName("李四");
        employee2.setPhone("13800138001"); // 相同的联系方式
        employee2.setEmail("<EMAIL>");
        
        Result result2 = employeeService.addEmployee(employee2);
        assertEquals(500, result2.getCode());
        assertEquals("该联系方式已被其他员工使用", result2.getMessage());
    }

    @Test
    public void testAddEmployeeWithUniqueEmail() {
        // 创建第一个员工
        Employee employee1 = new Employee();
        employee1.setName("王五");
        employee1.setPhone("13800138002");
        employee1.setEmail("<EMAIL>");
        
        Result result1 = employeeService.addEmployee(employee1);
        assertEquals(200, result1.getCode());
        
        // 尝试创建具有相同邮箱的第二个员工
        Employee employee2 = new Employee();
        employee2.setName("赵六");
        employee2.setPhone("13800138003");
        employee2.setEmail("<EMAIL>"); // 相同的邮箱
        
        Result result2 = employeeService.addEmployee(employee2);
        assertEquals(500, result2.getCode());
        assertEquals("该邮箱已被其他员工使用", result2.getMessage());
    }

    @Test
    public void testAddEmployeeWithEmptyPhoneAndEmail() {
        // 测试空的联系方式和邮箱应该可以添加
        Employee employee = new Employee();
        employee.setName("测试员工");
        employee.setPhone("");
        employee.setEmail("");
        
        Result result = employeeService.addEmployee(employee);
        assertEquals(200, result.getCode());
    }

    @Test
    public void testAddEmployeeWithNullPhoneAndEmail() {
        // 测试null的联系方式和邮箱应该可以添加
        Employee employee = new Employee();
        employee.setName("测试员工2");
        employee.setPhone(null);
        employee.setEmail(null);
        
        Result result = employeeService.addEmployee(employee);
        assertEquals(200, result.getCode());
    }
}
