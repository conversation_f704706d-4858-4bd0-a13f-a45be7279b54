package qidian.it.springboot.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.AdminService;


@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/admin")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    private final AdminService authenticationService;


    public AdminController(AdminService adminService) {
        this.authenticationService = adminService;
    }


    @GetMapping("/login")
    public Result authenticateUser(@RequestParam("username") String userIdentifier,
                                 @RequestParam("password") String userPassword) {
        logger.info("Authentication attempt for user: {}", userIdentifier);

        Result authResult = authenticationService.login(userIdentifier, userPassword);

        if (authResult.getCode().equals(Result.SUCCESS_CODE)) {
            logger.info("Successful authentication for user: {}", userIdentifier);
        } else {
            logger.warn("Failed authentication attempt for user: {}", userIdentifier);
        }

        return authResult;
    }


    @GetMapping("/register")
    public Result registerNewUser(@RequestParam("username") String userIdentifier,
                                @RequestParam("password") String userPassword,
                                @RequestParam("email") String emailAddress) {
        logger.info("Registration attempt for user: {} with email: {}", userIdentifier, emailAddress);

        Result registrationResult = authenticationService.register(userIdentifier, userPassword, emailAddress);

        if (registrationResult.getCode().equals(Result.SUCCESS_CODE)) {
            logger.info("Successful registration for user: {}", userIdentifier);
        } else {
            logger.warn("Failed registration attempt for user: {} - {}", userIdentifier, registrationResult.getMessage());
        }

        return registrationResult;
    }


    @GetMapping("/verifyAccount")
    public Result verifyUserAccount(@RequestParam("username") String userIdentifier,
                                  @RequestParam("email") String emailAddress) {
        logger.info("Account verification request for user: {} with email: {}", userIdentifier, emailAddress);

        Result verificationResult = authenticationService.verifyUsernameAndEmail(userIdentifier, emailAddress);

        if (verificationResult.getCode().equals(Result.SUCCESS_CODE)) {
            logger.info("Account verification successful for user: {}", userIdentifier);
        } else {
            logger.warn("Account verification failed for user: {}", userIdentifier);
        }

        return verificationResult;
    }


    @GetMapping("/resetPassword")
    public Result resetUserPassword(@RequestParam("username") String userIdentifier,
                                  @RequestParam("newPassword") String newUserPassword) {
        logger.info("Password reset request for user: {}", userIdentifier);

        Result resetResult = authenticationService.resetPassword(userIdentifier, newUserPassword);

        if (resetResult.getCode().equals(Result.SUCCESS_CODE)) {
            logger.info("Password reset successful for user: {}", userIdentifier);
        } else {
            logger.warn("Password reset failed for user: {}", userIdentifier);
        }

        return resetResult;
    }
}
