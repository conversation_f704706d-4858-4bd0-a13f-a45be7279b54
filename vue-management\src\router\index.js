import { createRouter, createWebHistory } from 'vue-router'

function isAuthed(){
  try{ return !!localStorage.getItem('admin') }catch(e){ return false }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'index',
      component: () => import('../views/index.vue'),
      children:[
        {
          path: '/dashboard',
          name: 'dashboard',
          component: () => import('../views/Dashboard.vue')
        },
        {
          path: '/employee/list',
          name: 'employeeList',
          component: () => import('../views/employee/EmployeeList.vue')
        },
        {
          path: '/department/list',
          name: 'departmentList',
          component: () => import('../views/department/DepartmentList.vue')
        },

      ]
    },
      {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
    {
      path: '/forgot-password',
      name: 'forgotPassword',
      component: () => import('../views/admin/ForgotPassword.vue')
    },

  ],
})

// 全局路由守卫：未登录则跳转到 /login
router.beforeEach((to, from, next)=>{
  const publicPaths = ['/login','/register','/forgot-password']
  if(publicPaths.includes(to.path)) return next()
  if(isAuthed()) return next()
  next({ path: '/login', query: { redirect: to.fullPath } })
})

export default router
