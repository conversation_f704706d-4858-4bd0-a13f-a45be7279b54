<template>
<div>
  <el-button type="warning" @click="addDept">添加部门</el-button>
</div>

  <el-table v-loading="loading" :data="tableData" style="width: 90%">
    <el-table-column label="部门编号" width="120">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">{{ scope.row.id }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="部门名称" width="120">
      <template #default="scope">
            <div>{{ scope.row.name }}</div>
      </template>
    </el-table-column>
    <el-table-column label="部门描述" width="120">
      <template #default="scope">
            <div>{{ scope.row.description }}</div>
      </template>
    </el-table-column>
   
    <el-table-column label="操作" >
      <template #default="scope">
        <el-button size="small" @click="handleEdit(scope.$index, scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="handleDelete(scope.$index, scope.row)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      :page-size="2"
      layout="prev, pager, next"
      :total="totalNum"
      @current-change="handleCurrentChange"
    />

  <el-dialog
    v-model="dialogVisible"
    title="员工添加"
    width="500"
  >
 <el-form :model="form" label-width="auto" style="max-width: 600px">
   <el-form-item label="部门编号">
      <el-input v-model="form.id" disabled/>
    </el-form-item>
     <el-form-item label="部门名称">
      <el-input v-model="form.name" />
    </el-form-item>

     <el-form-item label="部门描述">
      <el-input v-model="form.description" />
    </el-form-item>

 </el-form>



    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>



   <el-dialog
    v-model="dialogVisible2"
    title="用户修改"
    width="500"
  >
 <el-form :model="form2" label-width="auto" style="max-width: 600px">
 

     
     <el-form-item label="部门名称">
      <el-input v-model="form2.name" />
    </el-form-item>

     <el-form-item label="部门描述">
      <el-input v-model="form2.description" />
    </el-form-item>
 </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取消</el-button>
        <el-button type="primary" @click="submit2">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>


</template>

<script  setup>
import { ref,reactive,onMounted } from 'vue'
import { get, post } from '../../api/api.js'
import { ElNotification } from 'element-plus'
import {Search } from '@element-plus/icons-vue'

const loading=ref(true);
const dialogVisible=ref(false);

const dialogVisible2=ref(false);


const currentPage=ref(1);//当前页数，默认为第一页

const totalNum=ref(0);//记录总条数(数据库中的总数)

const form=reactive({});

const form2=reactive({});//保存添加的员工信息
const username=ref("");


const addDept=()=>{//添加员工
dialogVisible2.value=true;
}


const submit2=async()=>{
  console.log(form2);
  
const data=await get("/addDept",form2);
if(data.code==200){
  dialogVisible2.value=false;
  getData();
    ElNotification({
	    title: 'Success',
	    message: data.message,
	    type: 'success',
	  });
}else{
    ElNotification({
	    title: 'Error',
	    message: data.message,
	    type: 'error',
	  });
}
}

const handleCurrentChange = (val) => {//点击角标把当前页数赋值给currentPage
    currentPage.value=val;
    getData();
}


const handleEdit = (index, row) => {
dialogVisible.value=true;
  Object.assign(form,row);
}


const submit = () => { 
updateUser();
}


//编辑用户
const updateUser=async()=>{
const data=await get("/updateDept",form);
if(data.code==200){
    getData();
    dialogVisible.value=false;
         ElNotification({
	    title: 'Success',
	    message: data.message,
	    type: 'success',
	  });

}else{
    ElNotification({
	    title: 'Error',
	    message: data.message,
	    type: 'error',
	  });

}
}


const handleDelete = async(index, row) => {

const data=await get("/deleteDept",{id:row.id});

if(data.code==200){
   tableData.value.splice(index, 1);
    getData();
    dialogVisible.value=false;
         ElNotification({
	    title: 'Success',
	    message: data.message,
	    type: 'success',
	  });
}else{
    ElNotification({
	    title: 'Error',
	    message: data.message,
	    type: 'error',
	  });

}
}

const tableData = ref([]);


const getData = async()=>{
const data=await get("/getDeptInfo",{currentPage:currentPage.value});
tableData.value=data.data;
totalNum.value=data.code;
setTimeout(()=>{
  loading.value=false;
},300);
}
onMounted(async()=>{//获取数据,赋值给tableData
getData();

})
</script>
