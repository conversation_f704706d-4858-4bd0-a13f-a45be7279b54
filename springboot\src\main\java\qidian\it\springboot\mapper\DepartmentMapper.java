package qidian.it.springboot.mapper;

import qidian.it.springboot.entity.Department;
import java.util.List;

public interface DepartmentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Department record);

    int insertSelective(Department record);

    Department selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Department record);

    int updateByPrimaryKey(Department record);

    // 新增方法：支持部门管理功能
    List<Department> selectAll();

    Department selectByName(String name);

    List<Department> selectByNameLike(String name);
}