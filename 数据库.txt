CREATE TABLE admin (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码（加密存储）',
        email varchar(50)  COMMENT '邮箱'
);
CREATE TABLE employee (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '员工ID',
    name VARCHAR(50) NOT NULL COMMENT '员工姓名',
    gender varchar(10) DEFAULT '男' COMMENT '性别',
    age INT DEFAULT NULL COMMENT '年龄',
    phone VARCHAR(20) DEFAULT NULL COMMENT '联系方式',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    department_id BIGINT COMMENT '所属部门ID',
    position VARCHAR(50) DEFAULT NULL COMMENT '职位',
    hire_date DATE DEFAULT NULL COMMENT '入职日期',
    status TINYINT DEFAULT 1 COMMENT '在职状态（1：在职，0：离职）'
);
CREATE TABLE department (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '部门ID',
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    description VARCHAR(255) DEFAULT NULL COMMENT '部门描述'
);