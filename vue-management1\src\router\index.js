import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'index',
      component: () => import('../views/index.vue'),
      children:[
        {
          path: '/userList',
          name: 'userList',
          component: () => import('../views/user/userList.vue')
        },
         {
          path: '/empList',
          name: 'empList',
          component: () => import('../views/emp/empList.vue')
        },
        {
          path: '/deptList',
          name: 'deptList',
          component: () => import('../views/dept/deptList.vue')
        },
        
      ]
    },
      {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
  ],
})

export default router
