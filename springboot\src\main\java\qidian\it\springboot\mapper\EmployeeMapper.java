package qidian.it.springboot.mapper;

import qidian.it.springboot.entity.Employee;
import java.util.List;

public interface EmployeeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Employee record);

    int insertSelective(Employee record);

    Employee selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Employee record);

    int updateByPrimaryKey(Employee record);

    // 新增方法：支持员工管理功能
    List<Employee> selectAll();

    List<Employee> selectByDepartmentId(Long departmentId);

    int countByDepartmentId(Long departmentId);

    int deleteBatch(List<Long> ids);

    List<Employee> selectByNameLike(String name);

    // 新增方法：支持联系方式和邮箱唯一性检验
    Employee selectByPhone(String phone);

    Employee selectByEmail(String email);
}