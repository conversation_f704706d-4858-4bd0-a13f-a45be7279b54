import{h as f,c as _,b as o,w as r,a,u as w,o as g,e as V,E as m}from"./index-6989FsBU.js";import{g as b}from"./api-BgzO6Ksg.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const E={class:"form-container"},x={__name:"register",setup(P){w();const e=f({username:"",password:"",confirmPassword:""}),u=async()=>{if(!e.username){m({title:"Error",message:"用户名不能为空",type:"error"});return}if(e.password!=e.confirmPassword){m({title:"Error",message:"密码不一致",type:"error"});return}const l=await b("/register",e);l.code==200?(e.username="",e.password="",e.confirmPassword="",m({title:"Success",message:l.message,type:"success"})):m({title:"Error",message:l.message,type:"error"})};return(l,s)=>{const d=a("el-input"),n=a("el-form-item"),i=a("el-button"),c=a("el-form"),p=a("el-card");return g(),_("div",E,[o(p,{class:"form-card"},{default:r(()=>[o(c,{model:e,"label-width":"auto",class:"form"},{default:r(()=>[o(n,{label:"用户名"},{default:r(()=>[o(d,{modelValue:e.username,"onUpdate:modelValue":s[0]||(s[0]=t=>e.username=t)},null,8,["modelValue"])]),_:1}),o(n,{label:"密码"},{default:r(()=>[o(d,{modelValue:e.password,"onUpdate:modelValue":s[1]||(s[1]=t=>e.password=t),"show-password":""},null,8,["modelValue"])]),_:1}),o(n,{label:"确认密码"},{default:r(()=>[o(d,{modelValue:e.confirmPassword,"onUpdate:modelValue":s[2]||(s[2]=t=>e.confirmPassword=t),"show-password":""},null,8,["modelValue"])]),_:1}),o(n,{class:"btn-container"},{default:r(()=>[o(i,{type:"primary",onClick:u},{default:r(()=>s[3]||(s[3]=[V("确认",-1)])),_:1,__:[3]})]),_:1})]),_:1},8,["model"])]),_:1})])}}},N=y(x,[["__scopeId","data-v-fcdf3904"]]);export{N as default};
