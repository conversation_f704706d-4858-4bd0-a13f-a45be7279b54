package qidian.it.springboot.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.AdminMapper;
import qidian.it.springboot.service.AdminService;

import java.util.Objects;
import java.util.regex.Pattern;

/**
 * Administrator Service Implementation
 *
 * <AUTHOR>
 * @version 2.1.0
 *
 * Provides comprehensive administrator management services including:
 * - Secure authentication with BCrypt encryption
 * - User registration with validation
 * - Password recovery mechanisms
 * - Email format validation
 */
@Service
public class AdminServiceImpl implements AdminService {

    private static final Logger logger = LoggerFactory.getLogger(AdminServiceImpl.class);

    // Security and validation constants
    private static final String EMAIL_VALIDATION_REGEX =
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@" +
        "(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_VALIDATION_REGEX);

    // Error message constants
    private static final String EMPTY_USERNAME_ERROR = "用户名不能为空";
    private static final String EMPTY_PASSWORD_ERROR = "密码不能为空";
    private static final String EMPTY_EMAIL_ERROR = "邮箱不能为空";
    private static final String INVALID_CREDENTIALS_ERROR = "用户名或密码错误";
    private static final String INVALID_EMAIL_FORMAT_ERROR = "邮箱格式不正确";
    private static final String USERNAME_EXISTS_ERROR = "用户名已存在";
    private static final String EMAIL_EXISTS_ERROR = "邮箱已被注册";
    private static final String SERVER_BUSY_ERROR = "服务器繁忙，请稍后重试";

    private final AdminMapper userRepository;
    private final BCryptPasswordEncoder cryptographicEncoder;

    /**
     * Constructor injection for better testability
     * @param adminMapper Data access layer for admin operations
     */
    public AdminServiceImpl(AdminMapper adminMapper) {
        this.userRepository = adminMapper;
        this.cryptographicEncoder = new BCryptPasswordEncoder();
    }
    /**
     * Authenticate administrator with username and password
     *
     * @param userIdentifier Username for authentication
     * @param userPassword Plain text password
     * @return Authentication result with user data or error message
     */
    @Override
    public Result login(String userIdentifier, String userPassword) {
        logger.info("Processing login request for user: {}", userIdentifier);

        // Input validation
        if (userIdentifier == null || userIdentifier.trim().isEmpty()) {
            logger.warn("Login attempt with empty username");
            return Result.fail(EMPTY_USERNAME_ERROR);
        }

        if (userPassword == null || userPassword.trim().isEmpty()) {
            logger.warn("Login attempt with empty password for user: {}", userIdentifier);
            return Result.fail(EMPTY_PASSWORD_ERROR);
        }

        final String sanitizedUsername = userIdentifier.trim();

        // Retrieve user from database
        Admin userAccount = userRepository.selectByUsername(sanitizedUsername);
        if (Objects.isNull(userAccount)) {
            logger.warn("Login attempt for non-existent user: {}", sanitizedUsername);
            return Result.fail(INVALID_CREDENTIALS_ERROR);
        }

        // Verify password using BCrypt
        if (!cryptographicEncoder.matches(userPassword, userAccount.getPassword())) {
            logger.warn("Invalid password attempt for user: {}", sanitizedUsername);
            return Result.fail(INVALID_CREDENTIALS_ERROR);
        }

        // Successful authentication - remove sensitive data before returning
        userAccount.setPassword(null);
        logger.info("Successful login for user: {}", sanitizedUsername);

        return Result.success("登录成功", userAccount);
    }
    
    /**
     * Register new administrator account
     *
     * @param userIdentifier Desired username (must be unique)
     * @param userPassword Plain text password (will be encrypted)
     * @param emailAddress Email address for account verification
     * @return Registration result
     */
    @Override
    public Result register(String userIdentifier, String userPassword, String emailAddress) {
        logger.info("Processing registration request for user: {} with email: {}", userIdentifier, emailAddress);

        // Input validation
        if (userIdentifier == null || userIdentifier.trim().isEmpty()) {
            logger.warn("Registration attempt with empty username");
            return Result.fail(EMPTY_USERNAME_ERROR);
        }

        if (userPassword == null || userPassword.trim().isEmpty()) {
            logger.warn("Registration attempt with empty password for user: {}", userIdentifier);
            return Result.fail(EMPTY_PASSWORD_ERROR);
        }

        if (emailAddress == null || emailAddress.trim().isEmpty()) {
            logger.warn("Registration attempt with empty email for user: {}", userIdentifier);
            return Result.fail(EMPTY_EMAIL_ERROR);
        }

        final String sanitizedUsername = userIdentifier.trim();
        final String sanitizedEmail = emailAddress.trim();

        // Email format validation
        if (!validateEmailFormat(sanitizedEmail)) {
            logger.warn("Registration attempt with invalid email format: {}", sanitizedEmail);
            return Result.fail(INVALID_EMAIL_FORMAT_ERROR);
        }

        // Check username uniqueness
        Admin existingUserByUsername = userRepository.selectByUsername(sanitizedUsername);
        if (!Objects.isNull(existingUserByUsername)) {
            logger.warn("Registration attempt with existing username: {}", sanitizedUsername);
            return Result.fail(USERNAME_EXISTS_ERROR);
        }

        // Check email uniqueness
        Admin existingUserByEmail = userRepository.selectByEmail(sanitizedEmail);
        if (!Objects.isNull(existingUserByEmail)) {
            logger.warn("Registration attempt with existing email: {}", sanitizedEmail);
            return Result.fail(EMAIL_EXISTS_ERROR);
        }

        // Create new administrator account
        Admin newUserAccount = new Admin();
        newUserAccount.setUsername(sanitizedUsername);
        newUserAccount.setPassword(cryptographicEncoder.encode(userPassword));
        newUserAccount.setEmail(sanitizedEmail);

        // Persist to database
        if (userRepository.insertSelective(newUserAccount) > 0) {
            logger.info("Successful registration for user: {}", sanitizedUsername);
            return Result.success("注册成功");
        } else {
            logger.error("Database insertion failed for user: {}", sanitizedUsername);
            return Result.fail(SERVER_BUSY_ERROR);
        }
    }
    
    /**
     * Verify username and email combination for password recovery
     *
     * @param userIdentifier Username to verify
     * @param emailAddress Associated email address
     * @return Verification result
     */
    @Override
    public Result verifyUsernameAndEmail(String userIdentifier, String emailAddress) {
        logger.info("Processing account verification for user: {} with email: {}", userIdentifier, emailAddress);

        // Input validation
        if (userIdentifier == null || userIdentifier.trim().isEmpty()) {
            logger.warn("Account verification attempt with empty username");
            return Result.fail(EMPTY_USERNAME_ERROR);
        }

        if (emailAddress == null || emailAddress.trim().isEmpty()) {
            logger.warn("Account verification attempt with empty email for user: {}", userIdentifier);
            return Result.fail(EMPTY_EMAIL_ERROR);
        }

        final String sanitizedUsername = userIdentifier.trim();
        final String sanitizedEmail = emailAddress.trim();

        // Verify username and email combination
        Admin userAccount = userRepository.selectByUsernameAndEmail(sanitizedUsername, sanitizedEmail);
        if (Objects.isNull(userAccount)) {
            logger.warn("Account verification failed - no match for user: {} with email: {}", sanitizedUsername, sanitizedEmail);
            return Result.fail("用户名和邮箱不匹配");
        }

        logger.info("Account verification successful for user: {}", sanitizedUsername);
        return Result.success("验证成功，可以重设密码");
    }
    
    /**
     * Reset user password with new encrypted password
     *
     * @param userIdentifier Username for password reset
     * @param newUserPassword New plain text password
     * @return Password reset result
     */
    @Override
    public Result resetPassword(String userIdentifier, String newUserPassword) {
        logger.info("Processing password reset for user: {}", userIdentifier);

        // Input validation
        if (userIdentifier == null || userIdentifier.trim().isEmpty()) {
            logger.warn("Password reset attempt with empty username");
            return Result.fail(EMPTY_USERNAME_ERROR);
        }

        if (newUserPassword == null || newUserPassword.trim().isEmpty()) {
            logger.warn("Password reset attempt with empty password for user: {}", userIdentifier);
            return Result.fail("新密码不能为空");
        }

        final String sanitizedUsername = userIdentifier.trim();

        // Retrieve user account
        Admin userAccount = userRepository.selectByUsername(sanitizedUsername);
        if (Objects.isNull(userAccount)) {
            logger.warn("Password reset attempt for non-existent user: {}", sanitizedUsername);
            return Result.fail("用户不存在");
        }

        // Update password with BCrypt encryption
        userAccount.setPassword(cryptographicEncoder.encode(newUserPassword));

        if (userRepository.updateByPrimaryKeySelective(userAccount) > 0) {
            logger.info("Password reset successful for user: {}", sanitizedUsername);
            return Result.success("密码重设成功");
        } else {
            logger.error("Database update failed during password reset for user: {}", sanitizedUsername);
            return Result.fail(SERVER_BUSY_ERROR);
        }
    }

    /**
     * Validate email address format using regex pattern
     *
     * @param emailAddress Email address to validate
     * @return true if email format is valid, false otherwise
     */
    private boolean validateEmailFormat(String emailAddress) {
        return EMAIL_PATTERN.matcher(emailAddress).matches();
    }
}
