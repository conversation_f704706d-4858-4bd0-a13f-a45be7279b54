<template>

<div style="margin-bottom: 10px;">
    <el-input
      v-model="username"
      style="width: 240px"
      placeholder="请输入用户名"
      :prefix-icon="Search"
    />
    <el-button type="danger" @click="search">搜索</el-button>
</div>

  <el-table v-loading="loading" :data="tableData" style="width: 60%">
    <el-table-column label="用户编号" width="180">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">{{ scope.row.userId }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="用户名" width="180">
      <template #default="scope">
            <div>{{ scope.row.username }}</div>
      </template>
    </el-table-column>
    <el-table-column label="邮箱" width="180">
      <template #default="scope">
            <div>{{ scope.row.email }}</div>
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button size="small" @click="handleEdit(scope.$index, scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="handleDelete(scope.$index, scope.row)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      :page-size="2"
      layout="prev, pager, next"
      :total="totalNum"
      @current-change="handleCurrentChange"
    />

  <el-dialog
    v-model="dialogVisible"
    title="用户修改"
    width="500"
  >
 <el-form :model="form" label-width="auto" style="max-width: 600px">
    <el-form-item label="用户编号">
      <el-input v-model="form.userId" disabled/>
    </el-form-item>

     <el-form-item label="用户名">
      <el-input v-model="form.username" />
    </el-form-item>

     <el-form-item label="邮箱">
      <el-input v-model="form.email" />
    </el-form-item>

 </el-form>







    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>


</template>

<script  setup>
import { ref,reactive,onMounted } from 'vue'
import { get, post } from '../../api/api.js'
import { ElNotification } from 'element-plus'
import {Search } from '@element-plus/icons-vue'

const loading=ref(true);
const dialogVisible=ref(false);


const currentPage=ref(1);//当前页数，默认为第一页

const totalNum=ref(0);//记录总条数(数据库中的总数)

const form=reactive({});
const username=ref("");


const handleCurrentChange = (val) => {//点击角标把当前页数赋值给currentPage
    currentPage.value=val;
    getData();
}


const search=async()=>{ 
    const data=await get("/selectLikeUsername",{username:username.value});
      tableData.value=data.data;
}

const handleEdit = (index, row) => {
dialogVisible.value=true;
  Object.assign(form,row);
}


const submit = () => { 
updateUser();
}


//编辑用户
const updateUser=async()=>{
const data=await get("/updateUser",form);
if(data.code==200){
    getData();
    dialogVisible.value=false;
         ElNotification({
	    title: 'Success',
	    message: data.message,
	    type: 'success',
	  });

}else{
    ElNotification({
	    title: 'Error',
	    message: data.message,
	    type: 'error',
	  });

}
}




const handleDelete = (index, row) => {
    tableData.value.splice(index, 1);
  console.log(index, row)
}

const tableData = ref([]);



const getData = async()=>{
    const data=await get("/getUserInfo",{currentPage:currentPage.value});
console.log('data===>>',data);
tableData.value=data.data;
totalNum.value=data.code;
setTimeout(()=>{
  loading.value=false;
},300);
}
onMounted(async()=>{//获取数据,赋值给tableData
getData();

})
</script>
