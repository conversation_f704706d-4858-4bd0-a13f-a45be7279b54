package qidian.it.springboot.mapper;

import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.entity.User;

import java.util.List;

public interface AdminMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Admin record);

    int insertSelective(Admin record);

    Admin selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Admin record);

    int updateByPrimaryKey(Admin record);

    Admin selectByUsername(String username);
    List<Admin> getAllInfo();
}