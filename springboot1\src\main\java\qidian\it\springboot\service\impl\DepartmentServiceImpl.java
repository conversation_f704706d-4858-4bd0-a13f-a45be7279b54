package qidian.it.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.mapper.DepartmentMapper;
import qidian.it.springboot.mapper.EmployeeMapper;
import qidian.it.springboot.service.DepartmentService;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class DepartmentServiceImpl implements DepartmentService {
    @Resource
    DepartmentMapper departmentMapper;

    @Resource
    EmployeeMapper employeeMapper;

    @Override
    public Result addDept(Department department) {

        if(Objects.isNull(departmentMapper.selectByUsername(department.getName()))){
            if(departmentMapper.insertSelective(department)>0){
               return Result.success("添加成功");
            }
        }
        return Result.fail("服务器繁忙");
    }

    @Override
    public Result getDeptInfo(Integer currentPage) {
        PageHelper.startPage(currentPage, 10);
        //先获取数据库中所有的数据
        List<Department> userList = departmentMapper.getAllInfo();
        //截取所需要的数据
        PageInfo<Department> pageInfo = new PageInfo<>(userList);
        System.out.println("总数：" + pageInfo.getTotal());
        return Result.success((int) pageInfo.getTotal(),pageInfo.getList());
    }

    @Override
    public Result updateDept(Department department) {
        if(departmentMapper.updateByPrimaryKeySelective(department)>0){
            return Result.success("修改成功");
        }
        return Result.fail("服务器繁忙,请稍候");
    }

    @Override
    public Result deleteDept(Long id) {
        if(employeeMapper.selectByDeptId(id).size()>0){
            return Result.fail("该部门下有员工,无法删除");
        }
        if(departmentMapper.deleteByPrimaryKey(id)>0){
            return Result.success("删除成功");
        }
        return Result.fail("服务器繁忙,请稍候");
    }

}
