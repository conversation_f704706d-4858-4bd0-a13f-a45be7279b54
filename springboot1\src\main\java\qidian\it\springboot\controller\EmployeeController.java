package qidian.it.springboot.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.EmployeeServiceImpl;

@RestController
@CrossOrigin
public class EmployeeController {
    @Autowired
    EmployeeServiceImpl employeeService;

    @RequestMapping("/getUserInfo")
    public Result getUserInfo(Integer currentPage) {
        return employeeService.getUserInfo(currentPage);
    }

    @RequestMapping("/updateEmployee")
    public Result updateadminService(Employee employee) {
        return employeeService.updateUser(employee);
    }

    @RequestMapping("/selectLikeUsername")
    public Result selectLikeUsername(String username) {
        return employeeService.selectLikeUsername(username);
    }

    @RequestMapping("/addEmp")
    public Result addEmp(Employee employee) {
        return employeeService.addEmp(employee);
    }


    @RequestMapping("/deleteEmp")
    public Result deleteEmp(Long id) {
        return employeeService.deleteEmp(id);
    }



    }

