package qidian.it.springboot.service;

import qidian.it.springboot.entity.Result;

public interface AdminService {

    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    Result login(String username, String password);

    /**
     * 管理员注册
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱
     * @return 注册结果
     */
    Result register(String username, String password, String email);

    /**
     * 忘记密码 - 验证用户名和邮箱
     * @param username 用户名
     * @param email 邮箱
     * @return 验证结果
     */
    Result verifyUsernameAndEmail(String username, String email);

    /**
     * 重设密码
     * @param username 用户名
     * @param newPassword 新密码
     * @return 重设结果
     */
    Result resetPassword(String username, String newPassword);
}
