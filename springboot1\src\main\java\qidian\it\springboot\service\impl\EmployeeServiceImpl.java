package qidian.it.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.mapper.EmployeeMapper;
import qidian.it.springboot.service.EmployeeService;
import javax.annotation.Resource;
import java.util.List;

@Service
public class EmployeeServiceImpl implements EmployeeService {
    @Resource
    EmployeeMapper employeeMapper;


    @Override
    public Result addEmp(Employee employee) {
        if(employeeMapper.insertSelective(employee)>0){
            return Result.success("添加成功");
        }
        return Result.fail("服务器繁忙,请稍候");
    }


    @Override
    public Result getUserInfo(Integer currentPage) {
//当前显示第一页,每页2条数据
        PageHelper.startPage(currentPage, 10);
        //先获取数据库中所有的数据
        List<User> userList = employeeMapper.getAllInfo();
        //截取所需要的数据
        PageInfo<User> pageInfo = new PageInfo<>(userList);
        System.out.println("总数：" + pageInfo.getTotal());
        return Result.success((int) pageInfo.getTotal(),pageInfo.getList());
    }

    @Override
    public Result updateUser(Employee employee) {
            if(employeeMapper.updateByPrimaryKeySelective(employee)>0){
                return Result.success("修改成功");
            }
        return Result.fail("服务器繁忙,请稍候");
    }



    @Override
    public Result selectLikeUsername(String username) {
        return Result.success(employeeMapper.selectLikeUsername(username));
    }

    @Override
    public Result deleteEmp(Long id) {
        if(employeeMapper.deleteByPrimaryKey(id)>0){
            return Result.success("删除成功");
        }
        return Result.fail("服务器繁忙,请稍候");
    }

}
