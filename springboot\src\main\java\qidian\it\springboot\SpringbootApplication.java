package qidian.it.springboot;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = "qidian.it.springboot")
@MapperScan("qidian.it.springboot.mapper")
public class SpringbootApplication {

    /**
     * Application bootstrap method
     *
     * @param commandLineArgs Runtime arguments passed to the application
     */
    public static void main(String[] commandLineArgs) {
        SpringApplication.run(SpringbootApplication.class, commandLineArgs);
    }
}
