package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.service.impl.AdminServiceImpl;

@RestController
@CrossOrigin
public class AdminController {
   @Autowired
    AdminServiceImpl adminService;

    @RequestMapping("/login")
    public Result login(String username, String password) {
        return adminService.login(username, password);
    }

    @RequestMapping("/register")
    public Result register(String username, String password) {
        return adminService.register(username, password);
    }


}
