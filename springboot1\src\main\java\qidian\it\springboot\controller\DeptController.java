package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.DepartmentServiceImpl;

@RestController
@CrossOrigin
public class DeptController {
@Autowired
    DepartmentServiceImpl departmentService;
    @RequestMapping("/getDeptInfo")
    public Result getUserInfo(Integer currentPage) {
        return departmentService.getDeptInfo(currentPage);
    }

    @RequestMapping("/updateDept")
    public Result updateadminService(Department department) {
        return departmentService.updateDept(department);
    }

    @RequestMapping("/addDept")
    public Result addEmp(Department department) {
        return departmentService.addDept(department);
    }

    @RequestMapping("/deleteDept")
    public Result deleteEmp(Long id) {
        return departmentService.deleteDept(id);
    }


}
