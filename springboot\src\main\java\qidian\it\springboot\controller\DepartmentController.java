package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.DepartmentServiceImpl;

import javax.annotation.Resource;

@RestController
@CrossOrigin // 允许跨域
@RequestMapping("/department")
public class DepartmentController {
    
    @Resource
    private DepartmentServiceImpl departmentService;
    
    /**
     * 分页查询部门列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @RequestMapping("/list")
    public Result getDepartmentList(@RequestParam(defaultValue = "1") Integer currentPage,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        return departmentService.getDepartmentList(currentPage, pageSize);
    }
    
    /**
     * 查询所有部门（不分页）
     * @return 部门列表
     */
    @RequestMapping("/all")
    public Result getAllDepartments() {
        return departmentService.getAllDepartments();
    }
    
    /**
     * 新增部门
     * @param department 部门信息
     * @return 添加结果
     */
    @RequestMapping("/add")
    public Result addDepartment(Department department) {
        return departmentService.addDepartment(department);
    }
    
    /**
     * 根据ID查询部门
     * @param id 部门ID
     * @return 部门信息
     */
    @RequestMapping("/get")
    public Result getDepartmentById(@RequestParam Long id) {
        return departmentService.getDepartmentById(id);
    }
    
    /**
     * 更新部门信息
     * @param department 部门信息
     * @return 更新结果
     */
    @RequestMapping("/update")
    public Result updateDepartment(Department department) {
        return departmentService.updateDepartment(department);
    }
    
    /**
     * 删除部门
     * @param id 部门ID
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public Result deleteDepartment(@RequestParam Long id) {
        return departmentService.deleteDepartment(id);
    }
    
    /**
     * 根据部门名称模糊查询
     * @param name 部门名称关键字
     * @return 部门列表
     */
    @RequestMapping("/search")
    public Result searchDepartmentsByName(@RequestParam String name) {
        return departmentService.searchDepartmentsByName(name);
    }
}
