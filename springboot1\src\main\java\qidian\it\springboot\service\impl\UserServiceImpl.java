package qidian.it.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.mapper.UserMapper;
import qidian.it.springboot.service.UserService;
import qidian.it.springboot.util.MD5Util;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class UserServiceImpl implements UserService {
    @Resource
    UserMapper userMapper;

    @Override
    public Result login(String username, String password) {
        //登录规则:
        //1.判断用户名是否正确:
        //1).正确:继续判断密码是否正确->1.正确:登录成功->返回成功信息 2.错误:登录失败->返回失败信息
        //2).错误:登录失败->用户名不存在

        //通过用户名查询用户
      User user=  userMapper.selectByUsername(username);
      if(Objects.nonNull(user)){//用户名正确,继续判断密码是否正确
        if(MD5Util.checkPassword(password,user.getPassword())){//密码正确
            return Result.success("登录成功");
        }else{
            return Result.fail("密码错误");
        }
      }
        return Result.fail("用户名不存在");
    }

    @Override
    public Result register(String username, String password) {
        //注册规则:
        //1.用户名不允许重复
        //2.密码需要加密
     User user= userMapper.selectByUsername(username);
     if(Objects.isNull(user)){//用户名不存在,允许注册
        user=new User();
        user.setUsername(username);
        user.setPassword(MD5Util.MD5PassWord(password));//密码加密
          if(userMapper.insert(user)>0){//数据添加成功
              return Result.success("注册成功");
          }else{
              return Result.fail("服务器繁忙,请稍候");
          }
     }
        return Result.fail("用户名已存在");
    }

    @Override
    public Result getUserInfo(Integer currentPage) {
        //当前显示第一页,每页2条数据
        PageHelper.startPage(currentPage, 2);
        //先获取数据库中所有的数据
        List<User> userList = userMapper.getAllInfo();

        //截取所需要的数据
        PageInfo<User> pageInfo = new PageInfo<>(userList);
        System.out.println("总数：" + pageInfo.getTotal());
        return Result.success((int) pageInfo.getTotal(),pageInfo.getList());
    }


    @Override
    public Result updateUser(User user) {
    //修改用户信息
    //1.用户名不能重复

if(Objects.isNull(userMapper.selectByUsername(user.getUsername()))){
    if(userMapper.updateByPrimaryKeySelective(user)>0){
        return Result.success("修改成功");
    }else{
        return Result.fail("服务器繁忙,请稍候");
    }
}
        return Result.fail("用户名已存在");
    }

    @Override
    public Result selectLikeUsername(String username) {



        return Result.success(userMapper.selectLikeUsername(username));
    }
}
