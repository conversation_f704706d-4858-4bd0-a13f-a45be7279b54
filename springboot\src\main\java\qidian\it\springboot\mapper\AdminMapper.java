package qidian.it.springboot.mapper;

import org.apache.ibatis.annotations.Param;
import qidian.it.springboot.entity.Admin;

public interface AdminMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Admin record);

    int insertSelective(Admin record);

    Admin selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Admin record);

    int updateByPrimaryKey(Admin record);

    Admin selectByUsername(String username);

    Admin selectByEmail(String email);

    Admin selectByUsernameAndEmail(@Param("username") String username, @Param("email") String email);

}
