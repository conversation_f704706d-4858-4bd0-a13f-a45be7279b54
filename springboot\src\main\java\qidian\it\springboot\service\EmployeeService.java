package qidian.it.springboot.service;

import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;

import java.util.List;

public interface EmployeeService {
    
    /**
     * 分页查询员工列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    Result getEmployeeList(Integer currentPage, Integer pageSize);
    
    /**
     * 新增员工
     * @param employee 员工信息
     * @return 添加结果
     */
    Result addEmployee(Employee employee);
    
    /**
     * 根据ID查询员工
     * @param id 员工ID
     * @return 员工信息
     */
    Result getEmployeeById(Long id);
    
    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 更新结果
     */
    Result updateEmployee(Employee employee);
    
    /**
     * 删除员工
     * @param id 员工ID
     * @return 删除结果
     */
    Result deleteEmployee(Long id);
    
    /**
     * 批量删除员工
     * @param ids 员工ID列表
     * @return 删除结果
     */
    Result deleteEmployeeBatch(List<Long> ids);
    
    /**
     * 根据部门ID查询员工
     * @param departmentId 部门ID
     * @return 员工列表
     */
    Result getEmployeesByDepartment(Long departmentId);
    
    /**
     * 根据姓名模糊查询员工
     * @param name 姓名关键字
     * @return 员工列表
     */
    Result searchEmployeesByName(String name);
}
