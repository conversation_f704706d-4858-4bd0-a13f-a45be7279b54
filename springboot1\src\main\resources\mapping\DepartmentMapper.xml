<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springboot.mapper.DepartmentMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springboot.entity.Department" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, description
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from department
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByUsername" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from department
    where name=#{name}
  </select>

  <select id="getAllInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from department
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from department
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="qidian.it.springboot.entity.Department" >
    insert into department (id, name, description
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springboot.entity.Department" >
    insert into department
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="description != null" >
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springboot.entity.Department" >
    update department
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springboot.entity.Department" >
    update department
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>