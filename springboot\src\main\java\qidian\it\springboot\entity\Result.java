package qidian.it.springboot.entity;

/**
 * Standardized API Response Wrapper
 *
 * <AUTHOR>
 * @version 2.0.0
 *
 * Provides consistent response structure across all API endpoints
 * with support for pagination metadata and flexible data payloads.
 */
public class Result {

    // Response status constants
    public static final Integer SUCCESS_CODE = 200;
    public static final Integer ERROR_CODE = 500;

    private Integer code;
    private String message;
    private Object data;
    private Integer total; // Pagination metadata - total record count

    /**
     * Default constructor for framework compatibility
     */
    public Result() {
        // Empty constructor for serialization frameworks
    }

    /**
     * Constructor with status code only
     * @param statusCode HTTP-like status code
     */
    public Result(Integer statusCode) {
        this.code = statusCode;
    }

    /**
     * Constructor with status code and message
     * @param statusCode HTTP-like status code
     * @param responseMessage User-friendly message
     */
    public Result(Integer statusCode, String responseMessage) {
        this.code = statusCode;
        this.message = responseMessage;
    }

    /**
     * Constructor with data payload only
     * @param responseData Response payload
     */
    public Result(Object responseData) {
        this.data = responseData;
    }

    /**
     * Constructor with status code and data
     * @param statusCode HTTP-like status code
     * @param responseData Response payload
     */
    public Result(Integer statusCode, Object responseData) {
        this.code = statusCode;
        this.data = responseData;
    }

    /**
     * Constructor with status code, message and data
     * @param statusCode HTTP-like status code
     * @param responseMessage User-friendly message
     * @param responseData Response payload
     */
    public Result(Integer statusCode, String responseMessage, Object responseData) {
        this.code = statusCode;
        this.message = responseMessage;
        this.data = responseData;
    }

    /**
     * Full constructor with pagination support
     * @param statusCode HTTP-like status code
     * @param responseMessage User-friendly message
     * @param responseData Response payload
     * @param totalRecords Total record count for pagination
     */
    public Result(Integer statusCode, String responseMessage, Object responseData, Integer totalRecords) {
        this.code = statusCode;
        this.message = responseMessage;
        this.data = responseData;
        this.total = totalRecords;
    }

    /**
     * Create successful response with custom status code
     * @param statusCode Custom success code
     * @return Result instance
     */
    public static Result success(Integer statusCode) {
        return new Result(statusCode);
    }

    /**
     * Create successful response with message
     * @param successMessage Success message
     * @return Result instance with 200 status
     */
    public static Result success(String successMessage) {
        return new Result(SUCCESS_CODE, successMessage);
    }

    /**
     * Create successful response with data payload
     * @param responseData Data to return
     * @return Result instance with 200 status
     */
    public static Result success(Object responseData) {
        return new Result(SUCCESS_CODE, responseData);
    }

    /**
     * Create successful response with custom code and data
     * @param statusCode Custom status code
     * @param responseData Data to return
     * @return Result instance
     */
    public static Result success(Integer statusCode, Object responseData) {
        return new Result(statusCode, responseData);
    }

    /**
     * Create successful response with message and data
     * @param successMessage Success message
     * @param responseData Data to return
     * @return Result instance with 200 status
     */
    public static Result success(String successMessage, Object responseData) {
        return new Result(SUCCESS_CODE, successMessage, responseData);
    }

    /**
     * Create paginated successful response
     * @param responseData Paginated data list
     * @param totalRecords Total record count
     * @return Result instance with pagination metadata
     */
    public static Result successWithTotal(Object responseData, Integer totalRecords) {
        return new Result(SUCCESS_CODE, null, responseData, totalRecords);
    }

    /**
     * Create error response with message
     * @param errorMessage Error description
     * @return Result instance with 500 status
     */
    public static Result fail(String errorMessage) {
        return new Result(ERROR_CODE, errorMessage);
    }

    // Getter and Setter methods with enhanced documentation

    /**
     * Get response status code
     * @return HTTP-like status code
     */
    public Integer getCode() {
        return code;
    }

    /**
     * Set response status code
     * @param statusCode HTTP-like status code
     */
    public void setCode(Integer statusCode) {
        this.code = statusCode;
    }

    /**
     * Get response message
     * @return User-friendly message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Set response message
     * @param responseMessage User-friendly message
     */
    public void setMessage(String responseMessage) {
        this.message = responseMessage;
    }

    /**
     * Get response data payload
     * @return Response data object
     */
    public Object getData() {
        return data;
    }

    /**
     * Set response data payload
     * @param responseData Response data object
     */
    public void setData(Object responseData) {
        this.data = responseData;
    }

    /**
     * Get total record count for pagination
     * @return Total record count
     */
    public Integer getTotal() {
        return total;
    }

    /**
     * Set total record count for pagination
     * @param totalRecords Total record count
     */
    public void setTotal(Integer totalRecords) {
        this.total = totalRecords;
    }

    /**
     * String representation for debugging and logging
     * @return Formatted string representation
     */
    @Override
    public String toString() {
        return String.format("Result{code=%d, message='%s', data=%s, total=%d}",
                           code, message, data, total);
    }
}
