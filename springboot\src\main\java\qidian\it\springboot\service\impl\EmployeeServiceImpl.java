package qidian.it.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.EmployeeMapper;
import qidian.it.springboot.service.EmployeeService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class EmployeeServiceImpl implements EmployeeService {
    
    @Resource
    private EmployeeMapper employeeMapper;
    
    @Override
    public Result getEmployeeList(Integer currentPage, Integer pageSize) {
        // 设置默认值
        if (currentPage == null || currentPage < 1) {
            currentPage = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        
        // 分页查询
        PageHelper.startPage(currentPage, pageSize);
        List<Employee> employeeList = employeeMapper.selectAll();
        
        // 封装分页信息
        PageInfo<Employee> pageInfo = new PageInfo<>(employeeList);

        return Result.successWithTotal(pageInfo.getList(), (int) pageInfo.getTotal());
    }
    
    @Override
    public Result addEmployee(Employee employee) {
        // 参数校验
        if (employee == null) {
            return Result.fail("员工信息不能为空");
        }
        if (employee.getName() == null || employee.getName().trim().isEmpty()) {
            return Result.fail("员工姓名不能为空");
        }

        // 联系方式唯一性检验
        if (employee.getPhone() != null && !employee.getPhone().trim().isEmpty()) {
            Employee existingEmployeeByPhone = employeeMapper.selectByPhone(employee.getPhone().trim());
            if (!Objects.isNull(existingEmployeeByPhone)) {
                return Result.fail("该联系方式已被其他员工使用");
            }
        }

        // 邮箱唯一性检验
        if (employee.getEmail() != null && !employee.getEmail().trim().isEmpty()) {
            Employee existingEmployeeByEmail = employeeMapper.selectByEmail(employee.getEmail().trim());
            if (!Objects.isNull(existingEmployeeByEmail)) {
                return Result.fail("该邮箱已被其他员工使用");
            }
        }

        // 设置默认值
        if (employee.getGender() == null || employee.getGender().trim().isEmpty()) {
            employee.setGender("男");
        }
        if (employee.getStatus() == null) {
            employee.setStatus((byte) 1); // 默认在职
        }
        if (employee.getHireDate() == null) {
            employee.setHireDate(new Date()); // 默认当前日期
        }

        // 保存员工信息
        if (employeeMapper.insertSelective(employee) > 0) {
            return Result.success("员工添加成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result getEmployeeById(Long id) {
        if (id == null) {
            return Result.fail("员工ID不能为空");
        }
        
        Employee employee = employeeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(employee)) {
            return Result.fail("员工不存在");
        }
        
        return Result.success(employee);
    }
    
    @Override
    public Result updateEmployee(Employee employee) {
        // 参数校验
        if (employee == null || employee.getId() == null) {
            return Result.fail("员工ID不能为空");
        }

        // 检查员工是否存在
        Employee existingEmployee = employeeMapper.selectByPrimaryKey(employee.getId());
        if (Objects.isNull(existingEmployee)) {
            return Result.fail("员工不存在");
        }

        // 联系方式唯一性检验（排除当前员工）
        if (employee.getPhone() != null && !employee.getPhone().trim().isEmpty()) {
            Employee existingEmployeeByPhone = employeeMapper.selectByPhone(employee.getPhone().trim());
            if (!Objects.isNull(existingEmployeeByPhone) && !existingEmployeeByPhone.getId().equals(employee.getId())) {
                return Result.fail("该联系方式已被其他员工使用");
            }
        }

        // 邮箱唯一性检验（排除当前员工）
        if (employee.getEmail() != null && !employee.getEmail().trim().isEmpty()) {
            Employee existingEmployeeByEmail = employeeMapper.selectByEmail(employee.getEmail().trim());
            if (!Objects.isNull(existingEmployeeByEmail) && !existingEmployeeByEmail.getId().equals(employee.getId())) {
                return Result.fail("该邮箱已被其他员工使用");
            }
        }

        // 更新员工信息
        if (employeeMapper.updateByPrimaryKeySelective(employee) > 0) {
            return Result.success("员工信息更新成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result deleteEmployee(Long id) {
        if (id == null) {
            return Result.fail("员工ID不能为空");
        }
        
        // 检查员工是否存在
        Employee employee = employeeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(employee)) {
            return Result.fail("员工不存在");
        }
        
        // 删除员工
        if (employeeMapper.deleteByPrimaryKey(id) > 0) {
            return Result.success("员工删除成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result deleteEmployeeBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.fail("请选择要删除的员工");
        }
        
        // 批量删除
        if (employeeMapper.deleteBatch(ids) > 0) {
            return Result.success("员工批量删除成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result getEmployeesByDepartment(Long departmentId) {
        if (departmentId == null) {
            return Result.fail("部门ID不能为空");
        }
        
        List<Employee> employees = employeeMapper.selectByDepartmentId(departmentId);
        return Result.success(employees);
    }
    
    @Override
    public Result searchEmployeesByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return Result.fail("搜索关键字不能为空");
        }
        
        List<Employee> employees = employeeMapper.selectByNameLike(name.trim());
        return Result.success(employees);
    }
}
