package qidian.it.springboot.mapper;

import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.User;

import java.util.List;

public interface EmployeeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Employee record);

    int insertSelective(Employee record);

    Employee selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Employee record);

    List<Employee> selectByDeptId(Long id);

    int updateByPrimaryKey(Employee record);
    User selectByUsername(String username);
    List<User> selectLikeUsername(String username);
    List<User> getAllInfo();

}