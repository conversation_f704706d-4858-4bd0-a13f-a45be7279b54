package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.util.RedisUtil;
import javax.annotation.Resource;



@RestController
@CrossOrigin
public class MyController {


    @Resource
    RedisUtil redisUtil;
    @RequestMapping("/test")
    public String hello(){
        return "hello world";
    }


    @RequestMapping("/checkLogin")
    public Result checkLogin(String username){
        return Result.success(redisUtil.get(username, Admin.class));
    }


}
