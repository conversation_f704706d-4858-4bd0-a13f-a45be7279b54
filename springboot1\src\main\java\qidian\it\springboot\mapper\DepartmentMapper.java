package qidian.it.springboot.mapper;

import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.User;

import java.util.List;

public interface DepartmentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Department record);

    int insertSelective(Department record);

    Department selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Department record);

    int updateByPrimaryKey(Department record);

    User selectByUsername(String username);
    List<Department> getAllInfo();
}