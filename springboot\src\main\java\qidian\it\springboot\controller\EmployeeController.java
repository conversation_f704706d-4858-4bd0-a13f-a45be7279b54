package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.EmployeeServiceImpl;

import javax.annotation.Resource;
import java.util.List;

@RestController
@CrossOrigin // 允许跨域
@RequestMapping("/employee")
public class EmployeeController {
    
    @Resource
    private EmployeeServiceImpl employeeService;
    
    /**
     * 分页查询员工列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @RequestMapping("/list")
    public Result getEmployeeList(@RequestParam(defaultValue = "1") Integer currentPage,
                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        return employeeService.getEmployeeList(currentPage, pageSize);
    }
    
    /**
     * 新增员工
     * @param employee 员工信息
     * @return 添加结果
     */
    @RequestMapping("/add")
    public Result addEmployee(Employee employee) {
        return employeeService.addEmployee(employee);
    }
    
    /**
     * 根据ID查询员工
     * @param id 员工ID
     * @return 员工信息
     */
    @RequestMapping("/get")
    public Result getEmployeeById(@RequestParam Long id) {
        return employeeService.getEmployeeById(id);
    }
    
    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 更新结果
     */
    @RequestMapping("/update")
    public Result updateEmployee(Employee employee) {
        return employeeService.updateEmployee(employee);
    }
    
    /**
     * 删除员工
     * @param id 员工ID
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public Result deleteEmployee(@RequestParam Long id) {
        return employeeService.deleteEmployee(id);
    }
    
    /**
     * 批量删除员工
     * @param ids 员工ID列表
     * @return 删除结果
     */
    @RequestMapping("/deleteBatch")
    public Result deleteEmployeeBatch(@RequestBody List<Long> ids) {
        return employeeService.deleteEmployeeBatch(ids);
    }
    
    /**
     * 根据部门ID查询员工
     * @param departmentId 部门ID
     * @return 员工列表
     */
    @RequestMapping("/getByDepartment")
    public Result getEmployeesByDepartment(@RequestParam Long departmentId) {
        return employeeService.getEmployeesByDepartment(departmentId);
    }
    
    /**
     * 根据姓名模糊查询员工
     * @param name 姓名关键字
     * @return 员工列表
     */
    @RequestMapping("/search")
    public Result searchEmployeesByName(@RequestParam String name) {
        return employeeService.searchEmployeesByName(name);
    }
}
