import{d as R,u as V,r as B,a as l,c as x,o as v,b as e,w as t,e as o,f as i,g as L,R as N}from"./index-6989FsBU.js";const g={class:"container"},G=R({__name:"Aside",setup(k){const f=V(),p=(d,n)=>{},r=(d,n)=>{},m=d=>{f.push(d)},_=B("");return(d,n)=>{const b=l("HomeFilled"),a=l("el-icon"),s=l("el-menu-item"),u=l("el-sub-menu"),c=l("UserFilled"),w=l("Guide"),y=l("el-menu"),C=l("el-col"),F=l("el-row");return v(),x("div",g,[e(F,{class:"tac"},{default:t(()=>[e(C,{span:24},{default:t(()=>[e(y,{"active-text-color":"#ffd04b","background-color":"#545c64",class:"el-menu-vertical-demo","default-active":_.value,"text-color":"#fff",onOpen:p,onClose:r,onSelect:m,router:""},{default:t(()=>[e(u,{index:"1"},{title:t(()=>[e(a,null,{default:t(()=>[e(b)]),_:1}),n[0]||(n[0]=i("span",null,"首页",-1))]),default:t(()=>[e(s,{index:"/index"},{default:t(()=>n[1]||(n[1]=[o("大屏数据",-1)])),_:1,__:[1]}),e(u,{index:"1-4"},{title:t(()=>n[2]||(n[2]=[o("item four",-1)])),default:t(()=>[e(s,{index:"1-4-1"},{default:t(()=>n[3]||(n[3]=[o("item one",-1)])),_:1,__:[3]})]),_:1})]),_:1}),e(u,{index:"2"},{title:t(()=>[e(a,null,{default:t(()=>[e(c)]),_:1}),n[4]||(n[4]=i("span",null,"员工管理",-1))]),default:t(()=>[e(s,{index:"/empList"},{default:t(()=>n[5]||(n[5]=[o("员工列表",-1)])),_:1,__:[5]})]),_:1}),e(u,{index:"3"},{title:t(()=>[e(a,null,{default:t(()=>[e(c)]),_:1}),n[6]||(n[6]=i("span",null,"用户管理",-1))]),default:t(()=>[e(s,{index:"/userList"},{default:t(()=>n[7]||(n[7]=[o("用户列表",-1)])),_:1,__:[7]}),e(s,{index:"3-2"},{default:t(()=>n[8]||(n[8]=[o("用户添加",-1)])),_:1,__:[8]}),e(s,{index:"3-3"},{default:t(()=>n[9]||(n[9]=[o("用户修改",-1)])),_:1,__:[9]}),e(s,{index:"3-4"},{default:t(()=>n[10]||(n[10]=[o("用户删除",-1)])),_:1,__:[10]})]),_:1}),e(u,{index:"4"},{title:t(()=>[e(a,null,{default:t(()=>[e(w)]),_:1}),n[11]||(n[11]=i("span",null,"部门管理",-1))]),default:t(()=>[e(s,{index:"/deptList"},{default:t(()=>n[12]||(n[12]=[o("部门列表",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["default-active"])]),_:1})]),_:1})])}}}),H={class:"common-layout"},P={__name:"index",setup(k){return(f,p)=>{const r=l("el-aside"),m=l("el-main"),_=l("el-container");return v(),x("div",H,[e(_,null,{default:t(()=>[e(_,null,{default:t(()=>[e(r,{class:"aside"},{default:t(()=>[e(G)]),_:1}),e(m,{class:"main"},{default:t(()=>[e(L(N))]),_:1})]),_:1})]),_:1})])}}};export{P as default};
