package qidian.it.springboot.service;

import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;

public interface DepartmentService {
    
    /**
     * 分页查询部门列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    Result getDepartmentList(Integer currentPage, Integer pageSize);
    
    /**
     * 查询所有部门（不分页）
     * @return 部门列表
     */
    Result getAllDepartments();
    
    /**
     * 新增部门
     * @param department 部门信息
     * @return 添加结果
     */
    Result addDepartment(Department department);
    
    /**
     * 根据ID查询部门
     * @param id 部门ID
     * @return 部门信息
     */
    Result getDepartmentById(Long id);
    
    /**
     * 更新部门信息
     * @param department 部门信息
     * @return 更新结果
     */
    Result updateDepartment(Department department);
    
    /**
     * 删除部门
     * @param id 部门ID
     * @return 删除结果
     */
    Result deleteDepartment(Long id);
    
    /**
     * 根据部门名称模糊查询
     * @param name 部门名称关键字
     * @return 部门列表
     */
    Result searchDepartmentsByName(String name);
}
